{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,wPAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/label.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Label = registerClientReference(\n    function() { throw new Error(\"Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/label.tsx <module evaluation>\",\n    \"Label\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,kRAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/label.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Label = registerClientReference(\n    function() { throw new Error(\"Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/label.tsx\",\n    \"Label\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,kRAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-2 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,4JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/login/page.tsx"], "sourcesContent": ["import { Header } from '@/components/header';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport Link from 'next/link';\r\nimport React from 'react';\r\n\r\nconst Login = () => {\r\n  return (\r\n    <div className=\"min-h-screen bg-background\">\r\n      <Header />\r\n      <main className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-md mx-auto\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Sign In</CardTitle>\r\n              <CardDescription>\r\n                Enter your credentials to access your account\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"email\">Email</Label>\r\n                <Input id=\"email\" type=\"email\" placeholder=\"Enter your email\" />\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"password\">Password</Label>\r\n                <Input\r\n                  id=\"password\"\r\n                  type=\"password\"\r\n                  placeholder=\"Enter your password\"\r\n                />\r\n              </div>\r\n              <Button className=\"w-full\">Sign In</Button>\r\n              <p className=\"text-center text-sm text-muted-foreground\">\r\n                Don&apos;t have an account?{' '}\r\n                <Link href=\"/signup\" className=\"text-primary hover:underline\">\r\n                  Sign up\r\n                </Link>\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAOA;;;;;;;;AAGA,MAAM,QAAQ;IACZ,qBACE,wPAAC;QAAI,WAAU;;0BACb,wPAAC,yJAAM;;;;;0BACP,wPAAC;gBAAK,WAAU;0BACd,cAAA,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC,kJAAI;;0CACH,wPAAC,wJAAU;;kDACT,wPAAC,uJAAS;kDAAC;;;;;;kDACX,wPAAC,6JAAe;kDAAC;;;;;;;;;;;;0CAInB,wPAAC,yJAAW;gCAAC,WAAU;;kDACrB,wPAAC;wCAAI,WAAU;;0DACb,wPAAC,oJAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,wPAAC,oJAAK;gDAAC,IAAG;gDAAQ,MAAK;gDAAQ,aAAY;;;;;;;;;;;;kDAE7C,wPAAC;wCAAI,WAAU;;0DACb,wPAAC,oJAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,wPAAC,oJAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;;;;;;;;;;;;kDAGhB,wPAAC,sJAAM;wCAAC,WAAU;kDAAS;;;;;;kDAC3B,wPAAC;wCAAE,WAAU;;4CAA4C;4CAC3B;0DAC5B,wPAAC,iLAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9E;uCAEe", "debugId": null}}]}