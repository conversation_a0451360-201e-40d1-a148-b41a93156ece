{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "30685", "content-security-policy": "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", "content-type": "application/json; charset=utf-8", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "date": "Thu, 09 Oct 2025 10:46:08 GMT", "etag": "W/\"77dd-OR3D8o/UUWaKlAhfRgzFK9S0pIY\"", "keep-alive": "timeout=5", "origin-agent-cluster": "?1", "ratelimit-limit": "100", "ratelimit-policy": "100;w=900", "ratelimit-remaining": "99", "ratelimit-reset": "900", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=31536000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0"}, "body": "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", "status": 200, "url": "http://localhost:4000/api/posts"}, "revalidate": 60, "tags": []}